@import "tailwindcss";

/* TinyMCE editor styling */
.tinymce-wrapper .tox {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
}

.tinymce-wrapper .tox.mce-focused {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.tinymce-wrapper .tox .tox-toolbar {
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.tinymce-wrapper .tox .tox-edit-area {
  background: white;
}

/* Custom TinyMCE content styling */
.tinymce-wrapper .tox-edit-area iframe {
  background: white !important;
}

/* Ensure TinyMCE list styling */
.mce-content-body ul {
  list-style-type: disc;
  padding-left: 20px;
  margin: 0.5rem 0;
}

.mce-content-body ol {
  list-style-type: decimal;
  padding-left: 20px;
  margin: 0.5rem 0;
}

.mce-content-body li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

/* Ensure nested lists work properly */
.mce-content-body ul ul {
  list-style-type: circle;
  margin: 0.25rem 0;
}

.mce-content-body ul ul ul {
  list-style-type: square;
  margin: 0.25rem 0;
}

.mce-content-body ol ol {
  list-style-type: lower-alpha;
  margin: 0.25rem 0;
}

.mce-content-body ol ol ol {
  list-style-type: lower-roman;
  margin: 0.25rem 0;
}

/* Smooth scrolling for all scrollable elements */
* {
  scroll-behavior: smooth;
}

/* Ensure proper scrolling in preview containers */
.preview-container {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.preview-container::-webkit-scrollbar {
  width: 6px;
}

.preview-container::-webkit-scrollbar-track {
  background: transparent;
}

.preview-container::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.preview-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}


/* Preview styling (without !important as these shouldn't conflict) */
.prose ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.prose ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.prose li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

.prose ul ul {
  list-style-type: circle;
}

.prose ul ul ul {
  list-style-type: square;
}

/* Ensure list styles are preserved in all preview contexts */
[dangerouslySetInnerHTML] ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

[dangerouslySetInnerHTML] ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

[dangerouslySetInnerHTML] li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

/* TinyMCE placeholder styling handled in component */
