import { useRef, useImperativeHandle, forwardRef } from 'react';
import { Editor } from '@tinymce/tinymce-react';
import { Editor as TinyMCEEditor } from 'tinymce';

interface RichTextEditorProps {
  value?: string;
  onChange?: (content: string) => void;
  placeholder?: string;
  readOnly?: boolean;
  theme?: 'snow' | 'bubble';
  modules?: Record<string, unknown>;
  formats?: string[];
  className?: string;
}

export interface RichTextEditorRef {
  getEditor: () => TinyMCEEditor | null;
  focus: () => void;
  blur: () => void;
}

const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>(({
  value = '',
  onChange,
  placeholder = '',
  readOnly = false,
  className = ''
}, ref) => {
  const editorRef = useRef<TinyMCEEditor | null>(null);

  useImperativeHandle(ref, () => ({
    getEditor: () => editorRef.current,
    focus: () => editorRef.current?.focus(),
    blur: () => editorRef.current?.getBody()?.blur()
  }));

  return (
    <div className={`tinymce-wrapper ${className}`}>
      <Editor
        onInit={(_evt, editor) => {
          editorRef.current = editor;
        }}
        value={value}
        onEditorChange={(content) => {
          onChange?.(content);
        }}
        init={{
          height: 200,
          menubar: false,
          plugins: [
            'lists', 'link', 'autolink', 'charmap', 'searchreplace',
            'wordcount', 'fullscreen', 'insertdatetime'
          ],
          toolbar: 'bold italic underline | bullist numlist | h1 h2 | removeformat',
          toolbar_mode: 'wrap',
          content_style: `
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; 
              font-size: 14px; 
              line-height: 1.5;
              color: #374151;
            }
            ul, ol { padding-left: 20px; }
            li { margin-bottom: 0.25rem; }
            h1 { font-size: 1.5em; font-weight: bold; margin: 0.5em 0; }
            h2 { font-size: 1.3em; font-weight: bold; margin: 0.5em 0; }
          `,
          placeholder: placeholder,
          disabled: readOnly,
          skin: false,
          content_css: false,
          branding: false,
          resize: false,
          statusbar: false,
          elementpath: false,
          setup: (editor) => {
            editor.on('focus', () => {
              editor.getContainer().classList.add('mce-focused');
            });
            editor.on('blur', () => {
              editor.getContainer().classList.remove('mce-focused');
            });
          }
        }}
      />
    </div>
  );
});

RichTextEditor.displayName = 'RichTextEditor';

export default RichTextEditor;